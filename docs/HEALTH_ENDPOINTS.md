# Health Check Endpoints

This document describes the health check and monitoring endpoints available in the application.

## 📋 Overview

The application provides a comprehensive health check endpoint for monitoring and connectivity testing:

- **`/api/health`** - Comprehensive health check with detailed system information

## 🏥 Health Check Endpoint

### `/api/health`

Comprehensive health check endpoint that provides detailed information about the application's health status.

#### **GET /api/health**

Returns detailed health information including system metrics and service status.

**Response Format:**

```json
{
  "status": "healthy" | "unhealthy",
  "timestamp": "2025-06-28T12:18:19.439Z",
  "uptime": 94,
  "version": "1.0.0",
  "environment": "development",
  "services": {
    "database": "healthy" | "unhealthy" | "unknown",
    "redis": "healthy" | "unhealthy" | "unknown",
    "external_apis": "healthy" | "unhealthy" | "unknown"
  },
  "memory": {
    "used": 79,
    "total": 87,
    "percentage": 90
  }
}
```

**Status Codes:**

- `200` - Application is healthy
- `503` - Application is unhealthy

**Headers:**

- `Cache-Control: no-cache, no-store, must-revalidate`
- `X-Response-Time: {time}ms`

#### **HEAD /api/health**

Lightweight health check that returns only status code and headers (no body).

**Status Codes:**

- `200` - Application is healthy
- `503` - Application is unhealthy

**Headers:**

- `Cache-Control: no-cache, no-store, must-revalidate`
- `X-Health-Status: healthy | unhealthy`

#### **OPTIONS /api/health**

CORS preflight support.

**Status Codes:**

- `200` - Always successful

**Headers:**

- `Allow: GET, HEAD, OPTIONS`
- `Cache-Control: no-cache, no-store, must-revalidate`

## 🔧 Usage Examples

### Basic Health Check

```bash
# Full health information
curl http://localhost:3000/api/health

# Quick health status (HEAD request)
curl -I http://localhost:3000/api/health
```

### Load Balancer Configuration

For load balancers and monitoring tools:

```yaml
# Example for AWS ALB Target Group
health_check:
    path: '/api/health'
    method: 'HEAD'
    healthy_threshold: 2
    unhealthy_threshold: 3
    timeout: 5
    interval: 30
```

```yaml
# Example for Kubernetes Liveness Probe
livenessProbe:
    httpGet:
        path: /api/health
        port: 3000
        httpHeaders:
            - name: Accept
              value: application/json
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
```

```yaml
# Example for Kubernetes Readiness Probe
readinessProbe:
    httpGet:
        path: /api/health
        port: 3000
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 2
```

## 📊 Monitoring Integration

### Prometheus Metrics

The health endpoints can be used with monitoring tools:

```yaml
# Example Prometheus scrape config
scrape_configs:
    - job_name: 'vocab-app'
      static_configs:
          - targets: ['localhost:3000']
      metrics_path: '/api/health'
      scrape_interval: 30s
```

### Uptime Monitoring

For uptime monitoring services:

```bash
# Health check for uptime monitoring
curl -f http://localhost:3000/api/health
```

## 🔍 Health Check Components

### Database Health

- Tests database connectivity using Prisma client
- Executes simple query: `SELECT 1`
- Returns `healthy`, `unhealthy`, or `unknown`

### Memory Usage

- Reports heap memory usage in MB
- Includes used, total, and percentage
- Only available in Node.js environment

### System Information

- Application uptime in seconds
- Environment (development/production)
- Application version from package.json

## 🚨 Error Handling

### Health Check Failures

- Database connection failures result in `unhealthy` status
- System errors are logged with structured logging
- Graceful degradation for partial failures

### Response Codes

- `200` - All systems healthy
- `503` - One or more systems unhealthy
- `500` - Internal server error (rare)

## 🔒 Security

### Public Access

Both endpoints are publicly accessible and bypass authentication:

- No authentication required
- Safe to expose to load balancers
- No sensitive information exposed

### Rate Limiting

Consider implementing rate limiting for production:

```typescript
// Example rate limiting
const rateLimiter = rateLimit({
	windowMs: 1 * 60 * 1000, // 1 minute
	max: 60, // 60 requests per minute
	message: 'Too many health check requests',
});
```

## 📈 Performance

### Response Times

- **Health**: < 100ms (includes database check)

### Caching

- No caching applied (always fresh status)
- `Cache-Control` headers prevent caching
- Real-time health status

## 🛠️ Development

### Local Testing

```bash
# Start development server
yarn dev

# Test health endpoint
curl http://localhost:3000/api/health
```

### Custom Health Checks

Add custom health checks by extending the health endpoint:

```typescript
// Add to /api/health/route.ts
async function checkCustomService(): Promise<'healthy' | 'unhealthy'> {
	try {
		// Your custom health check logic
		return 'healthy';
	} catch {
		return 'unhealthy';
	}
}
```

## 📝 Notes

- Health endpoints are excluded from authentication middleware
- All responses include appropriate cache headers
- Structured error logging for debugging
- Compatible with standard monitoring tools
- Follows REST API conventions

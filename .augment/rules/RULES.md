---
type: 'manual'
---

<<<<<<< ours
# VOCAB PROJECT - LLM LONG-TERM MEMORY

## PROJECT OVERVIEW

**Vocab** is a comprehensive AI-powered vocabulary learning application built with Next.js 15, designed to help users expand their vocabulary through spaced repetition, AI-generated content, and organized learning collections.

## TECHNOLOGY STACK

### Frontend

-   **Framework**: Next.js 15 with App Router
-   **Language**: TypeScript
-   **Styling**: Tailwind CSS v4 with custom animations
-   **UI Components**: Radix UI primitives with custom design system
-   **State Management**: React Context API + Custom hooks
-   **Internationalization**: i18next with next-i18next
-   **Animations**: Framer Motion
-   **Icons**: Lucide React
-   **Package Manager**: Yarn v4.9.2

### Backend

-   **Runtime**: Node.js with Next.js API routes
-   **Database**: PostgreSQL with Prisma ORM
-   **Authentication**: JWT with multiple providers (Telegram, Google, Username/Password)
-   **AI Integration**: OpenAI GPT-4o-mini for content generation
-   **Caching**: Node-cache for server-side caching with file-based persistence
-   **Security**: Comprehensive security headers, CSRF protection, rate limiting

### Infrastructure

-   **Deployment**: Vercel with automatic CI/CD
-   **Development**: Docker Compose for local PostgreSQL
-   **Environment**: Environment-based configuration
-   **PWA**: Service worker and manifest for offline support

## ARCHITECTURE PATTERNS

### Key Design Principles

1. **Separation of Concerns**: Clear separation between API, Service, and Repository layers
2. **Dependency Injection**: Centralized service wiring in `wire.ts`
3. **Type Safety**: Comprehensive TypeScript usage with Prisma-generated types
4. **Security First**: Multiple security layers with validation, rate limiting, CSRF protection
5. **Performance**: Caching strategies, optimized LLM usage, skeleton loading states
   CES

6. **Package Management**: Always use yarn, never npm
7. **Database**: PostgreSQL only, use Prisma for all database operations
8. **Authentication**: JWT-based with multiple provider support
9. **AI Integration**: OpenAI GPT-4o-mini with optimization focus
10. **UI/UX**: Accessibility-first design with comprehensive ARIA support
11. **Security**: Defense-in-depth approach with multiple security layers
12. **Performance**: Optimize for mobile-first responsive design
13. **Internationalization**: Complete i18n support for EN/VI languages

This project represents a sophisticated, production-ready vocabulary learning platform with enterprise-grade architecture, comprehensive security, and advanced AI integration.

## IMPLEMENTATION PATTERNS

### Component Patterns

-   **Client Components**: Use 'use client' directive, handle user interactions
-   **Server Components**: Default, handle data fetching and static content
-   **Accessibility**: All components include ARIA labels and keyboard navigation
-   **Loading States**: Skeleton components for all async operations

### Error Handling

-   **API Errors**: Centralized error handling with consistent response format
-   **Client Errors**: Error boundaries with user-friendly messages
-   **Validation Errors**: Zod schema validation with detailed error messages
-   **LLM Errors**: Retry logic with exponential backoff

### Security Layers

1. **Middleware Level**: Rate limiting, security headers, CSRF protection
2. **API Level**: Input validation, authentication checks
3. **Service Level**: Business logic validation
4. **Database Level**: Prisma type safety and query optimization

## CODING CONVENTIONS

### File Naming

-   **Components**: PascalCase (e.g., `WordCard.tsx`)
-   **Services**: kebab-case with suffix (e.g., `word.service.ts`)
-   **API Routes**: kebab-case (e.g., `collection-stats.api.ts`)
-   **Types**: PascalCase with suffix (e.g., `WordDetail.ts`)

## TESTING & QUALITY ASSURANCE

### Code Quality

-   **ESLint**: Strict linting rules with Prettier integration
-   **TypeScript**: No-emit compilation for type checking
-   **Performance**: React Scan for performance monitoring

### Manual Testing Checklist

-   [ ] Authentication flows (all providers)
-   [ ] Collection CRUD operations
-   [ ] Vocabulary generation and review
-   [ ] Paragraph practice and Q&A
-   [ ] Grammar practice with error correction
-   [ ] Progress tracking and statistics
-   [ ] Internationalization (EN/VI switching)
-   [ ] Mobile responsiveness
-   [ ] Accessibility (keyboard navigation, screen readers)

### Performance Monitoring

-   **LLM Usage**: Token consumption tracking and optimization
-   **Database Queries**: Query performance monitoring
-   **Client Performance**: Core Web Vitals tracking
-   **Error Rates**: API error monitoring and alerting
||||||| ancestor
# VOCAB PROJECT - LLM LONG-TERM MEMORY

## PROJECT OVERVIEW

**Vocab** is a comprehensive AI-powered vocabulary learning application built with Next.js 15, designed to help users expand their vocabulary through spaced repetition, AI-generated content, and organized learning collections.

### Core Purpose

-   **Primary Goal**: Vocabulary learning through spaced repetition and AI-powered content generation
-   **Target Languages**: English (EN) and Vietnamese (VI) with bidirectional learning support
-   **Learning Methods**: Vocabulary practice, paragraph reading, Q&A exercises, grammar practice, multiple choice questions

## TECHNOLOGY STACK

### Frontend

-   **Framework**: Next.js 15 with App Router
-   **Language**: TypeScript
-   **Styling**: Tailwind CSS v4 with custom animations
-   **UI Components**: Radix UI primitives with custom design system
-   **State Management**: React Context API + Custom hooks
-   **Internationalization**: i18next with next-i18next
-   **Animations**: Framer Motion
-   **Icons**: Lucide React
-   **Package Manager**: Yarn v4.9.2

### Backend

-   **Runtime**: Node.js with Next.js API routes
-   **Database**: PostgreSQL with Prisma ORM
-   **Authentication**: JWT with multiple providers (Telegram, Google, Username/Password)
-   **AI Integration**: OpenAI GPT-4o-mini for content generation
-   **Caching**: Node-cache for server-side caching with file-based persistence
-   **Security**: Comprehensive security headers, CSRF protection, rate limiting

### Infrastructure

-   **Deployment**: Vercel with automatic CI/CD
-   **Development**: Docker Compose for local PostgreSQL
-   **Environment**: Environment-based configuration
-   **PWA**: Service worker and manifest for offline support

## ARCHITECTURE PATTERNS

### Clean Architecture Implementation

```
src/
├── app/                     # Next.js App Router pages & layouts
├── backend/                 # Server-side business logic
│   ├── api/                # API endpoint handlers (8 files)
│   ├── services/           # Business logic layer (10 services)
│   ├── repositories/       # Data access layer (7 repositories)
│   ├── middleware/         # Server middleware
│   └── wire.ts            # Dependency injection container
├── components/             # Reusable UI components
├── contexts/              # React Context providers
├── hooks/                 # Custom React hooks
├── lib/                   # Utilities and shared logic
├── types/                 # TypeScript type definitions
└── config/               # Configuration management
```

### Key Design Principles

1. **Separation of Concerns**: Clear separation between API, Service, and Repository layers
2. **Dependency Injection**: Centralized service wiring in `wire.ts`
3. **Type Safety**: Comprehensive TypeScript usage with Prisma-generated types
4. **Security First**: Multiple security layers with validation, rate limiting, CSRF protection
5. **Performance**: Caching strategies, optimized LLM usage, skeleton loading states

## CORE FEATURES

### 1. Collection Management

-   **Purpose**: Organize vocabulary learning into themed collections
-   **Features**: CRUD operations, word/paragraph management, language pair configuration
-   **Key Files**: `collection.service.ts`, `collection.api.ts`, `collection.repository.ts`

### 2. Vocabulary System

-   **Word Management**: Search, create, review vocabulary with definitions, examples, IPA
-   **Spaced Repetition**: Track last seen words and review intervals
-   **AI Generation**: Generate vocabulary based on keywords and difficulty levels
-   **Key Files**: `word.service.ts`, `last-seen-word.service.ts`, `llm.service.ts`

### 3. AI-Powered Content Generation

-   **LLM Integration**: OpenAI GPT-4o-mini for content generation
-   **Content Types**: Vocabulary words, paragraphs, questions, grammar exercises
-   **Optimization**: Token usage optimization, caching, prompt engineering
-   **Key Files**: `llm.service.ts`, `cache.service.ts`

### 4. Practice Modes

-   **Vocabulary Review**: Spaced repetition-based word review
-   **Multiple Choice**: MCQ practice for vocabulary
-   **Paragraph Practice**: Reading comprehension with Q&A
-   **Grammar Practice**: Error correction exercises with difficulty levels
-   **Translation Practice**: Bidirectional translation exercises

### 5. Progress Tracking

-   **Collection Stats**: Daily tracking of words reviewed, practice submissions
-   **User Analytics**: Learning patterns and progress visualization
-   **Key Files**: `collection-stats.service.ts`, stats pages

## DATABASE SCHEMA

### Core Models

-   **User**: Authentication, preferences, provider integration
-   **Collection**: Learning collections with language pairs
-   **Word**: Vocabulary items with definitions, examples, IPA
-   **Definition**: Word definitions with parts of speech, explanations, examples
-   **LastSeenWord**: Spaced repetition tracking
-   **Paragraph**: Reading materials with difficulty levels
-   **CollectionStats**: Daily progress tracking
-   **Feedback**: User feedback system

### Key Relationships

-   Users have many Collections
-   Collections contain word_ids and paragraph_ids arrays
-   Words have multiple Definitions
-   Definitions have multiple Explains and Examples
-   Users track LastSeenWords for spaced repetition

## DEVELOPMENT GUIDELINES

### Code Organization

1. **API Layer** (`src/backend/api`): Request handling, validation, authentication
2. **Service Layer** (`src/backend/services`): Business logic, orchestration
3. **Repository Layer** (`src/backend/repositories`): Data access, Prisma operations
4. **Component Layer** (`src/components`): Reusable UI components with accessibility
5. **Page Layer** (`src/app`): Next.js pages with client/server separation

### Security Implementation

-   **Input Validation**: Zod schemas for all API inputs
-   **Rate Limiting**: Tiered rate limits for different endpoint types
-   **CSRF Protection**: Double submit cookie pattern
-   **Security Headers**: Comprehensive security headers via middleware
-   **Authentication**: JWT-based with secure cookie storage

### Performance Optimization

-   **Caching Strategy**: Multi-tier caching with TTL-based invalidation
-   **LLM Optimization**: Token usage tracking, prompt optimization, response caching
-   **Loading States**: Skeleton components for better UX
-   **Image Optimization**: Next.js Image component with optimization

### Internationalization

-   **i18n Implementation**: Complete translation system with next-i18next
-   **Language Support**: English and Vietnamese with RTL considerations
-   **Translation Keys**: Structured translation keys for maintainability

## ENVIRONMENT CONFIGURATION

### Required Environment Variables

```bash
DATABASE_URL="postgresql://..."
JWT_SECRET="your-secret-key"
LLM_OPENAI_API_KEY="sk-..."
LLM_OPENAI_MODEL="gpt-4o-mini"
TELEGRAM_BOT_TOKEN="..." # Optional
GOOGLE_CLIENT_ID="..." # Optional
```

### Feature Flags

-   `FEATURE_GOOGLE_LOGIN`: Enable/disable Google OAuth
-   `LLM_OPTIMIZATION_ENABLED`: Enable LLM optimizations
-   `LLM_CACHING_ENABLED`: Enable LLM response caching

## DEVELOPMENT WORKFLOW

### Scripts

-   `yarn dev`: Development server with Turbopack
-   `yarn build`: Production build with Prisma migration
-   `yarn lint`: TypeScript type checking
-   `yarn p:m`: Prisma migrate dev
-   `yarn p:s`: Prisma Studio
-   `yarn dup`: Docker Compose up for local database

### Testing Strategy

-   TypeScript compilation for type safety
-   Manual testing with comprehensive error handling
-   Performance monitoring with React Scan

## FUTURE ROADMAP

The project has extensive documentation for advanced features in the `docs/` directory:

-   Advanced ML algorithms for personalized learning
-   Gamification and social learning features
-   Enterprise features and integrations
-   Neural science research applications
-   Edge computing and local LLM deployment
-   Visual learning and memory palace techniques

## KEY CONSTRAINTS & PREFERENCES

1. **Package Management**: Always use yarn, never npm
2. **Database**: PostgreSQL only, use Prisma for all database operations
3. **Authentication**: JWT-based with multiple provider support
4. **AI Integration**: OpenAI GPT-4o-mini with optimization focus
5. **UI/UX**: Accessibility-first design with comprehensive ARIA support
6. **Security**: Defense-in-depth approach with multiple security layers
7. **Performance**: Optimize for mobile-first responsive design
8. **Internationalization**: Complete i18n support for EN/VI languages

This project represents a sophisticated, production-ready vocabulary learning platform with enterprise-grade architecture, comprehensive security, and advanced AI integration.

## IMPLEMENTATION PATTERNS

### Service Layer Pattern

```typescript
// All services follow this interface pattern
export interface ServiceName {
	methodName(params): Promise<ReturnType>;
}

export class ServiceNameImpl implements ServiceName {
	constructor(private repository: Repository) {}

	async methodName(params): Promise<ReturnType> {
		// Business logic implementation
	}
}
```

### Repository Pattern

```typescript
// All repositories extend BaseRepository
export interface SpecificRepository extends BaseRepository<Model> {
	specificMethod(params): Promise<Model[]>;
}

export class SpecificRepositoryImpl
	extends BaseRepositoryImpl<Model>
	implements SpecificRepository {
	// Prisma-specific data access logic
}
```

### API Handler Pattern

```typescript
// All API handlers follow this structure
export async function handler(req: NextRequest) {
	try {
		// 1. Authentication check
		// 2. Input validation with Zod
		// 3. Service method call
		// 4. Response formatting
		return NextResponse.json(result);
	} catch (error) {
		return handleApiError(error);
	}
}
```

### Component Patterns

-   **Client Components**: Use 'use client' directive, handle user interactions
-   **Server Components**: Default, handle data fetching and static content
-   **Accessibility**: All components include ARIA labels and keyboard navigation
-   **Loading States**: Skeleton components for all async operations

### Error Handling

-   **API Errors**: Centralized error handling with consistent response format
-   **Client Errors**: Error boundaries with user-friendly messages
-   **Validation Errors**: Zod schema validation with detailed error messages
-   **LLM Errors**: Retry logic with exponential backoff

### Caching Strategy

-   **LLM Responses**: File-based caching with TTL (7 days for vocabulary, 3 days for paragraphs)
-   **Database Queries**: In-memory caching for frequently accessed data
-   **Static Assets**: Next.js automatic caching and optimization

### Security Layers

1. **Middleware Level**: Rate limiting, security headers, CSRF protection
2. **API Level**: Input validation, authentication checks
3. **Service Level**: Business logic validation
4. **Database Level**: Prisma type safety and query optimization

## CODING CONVENTIONS

### File Naming

-   **Components**: PascalCase (e.g., `WordCard.tsx`)
-   **Services**: kebab-case with suffix (e.g., `word.service.ts`)
-   **API Routes**: kebab-case (e.g., `collection-stats.api.ts`)
-   **Types**: PascalCase with suffix (e.g., `WordDetail.ts`)

### Import Organization

```typescript
// 1. External libraries
import { NextRequest } from 'next/server';
import { z } from 'zod';

// 2. Internal modules (absolute imports with @/)
import { WordService } from '@/backend/services';
import { validateRequest } from '@/lib/validation';

// 3. Relative imports
import './styles.css';
```

### TypeScript Usage

-   **Strict Mode**: All TypeScript strict checks enabled
-   **Type Definitions**: Comprehensive types for all data structures
-   **Prisma Types**: Use generated Prisma types as base, extend with business logic
-   **Zod Schemas**: Runtime validation schemas for all API inputs

### Component Structure

```typescript
'use client'; // If client component

import { ComponentProps } from 'react';
import { useTranslation } from '@/contexts';

interface Props extends ComponentProps<'div'> {
	specificProp: string;
}

export function ComponentName({ specificProp, ...props }: Props) {
	const { t } = useTranslation();

	return (
		<div {...props} role="..." aria-label={t('...')}>
			{/* Component content */}
		</div>
	);
}
```

## TESTING & QUALITY ASSURANCE

### Code Quality

-   **ESLint**: Strict linting rules with Prettier integration
-   **TypeScript**: No-emit compilation for type checking
-   **Performance**: React Scan for performance monitoring

### Manual Testing Checklist

-   [ ] Authentication flows (all providers)
-   [ ] Collection CRUD operations
-   [ ] Vocabulary generation and review
-   [ ] Paragraph practice and Q&A
-   [ ] Grammar practice with error correction
-   [ ] Progress tracking and statistics
-   [ ] Internationalization (EN/VI switching)
-   [ ] Mobile responsiveness
-   [ ] Accessibility (keyboard navigation, screen readers)

### Performance Monitoring

-   **LLM Usage**: Token consumption tracking and optimization
-   **Database Queries**: Query performance monitoring
-   **Client Performance**: Core Web Vitals tracking
-   **Error Rates**: API error monitoring and alerting

## DEPLOYMENT & OPERATIONS

### Vercel Configuration

-   **Build Command**: `yarn build` (includes Prisma migration)
-   **Framework**: Next.js with automatic optimization
-   **Environment**: Production environment variables
-   **Regions**: Singapore (sin1) for optimal performance

### Database Management

-   **Migrations**: Prisma migrate for schema changes
-   **Seeding**: Manual data seeding for development
-   **Backup**: Automated backups via hosting provider
-   **Monitoring**: Query performance and connection monitoring

### Security Operations

-   **SSL/TLS**: Automatic HTTPS via Vercel
-   **Headers**: Security headers via vercel.json
-   **Secrets**: Environment variables for sensitive data
-   **Monitoring**: Security event logging and monitoring

This comprehensive documentation ensures any LLM working with this codebase understands the full context, patterns, and constraints of the Vocab learning application.
=======
- Luôn luôn test browser trên port 3000 mà không cần phải run dev command.
- Nếu có viết translation key hãy thêm translation key vào translation context.
- Dự án sử dụng mô hình DDD.
>>>>>>> theirs

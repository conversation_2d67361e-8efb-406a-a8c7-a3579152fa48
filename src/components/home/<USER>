'use client';

// Removed direct backend API import - now using HTTP request
import { <PERSON><PERSON>, <PERSON>ading<PERSON><PERSON><PERSON>, Textarea, Translate, useToast } from '@/components/ui';
import { FEEDBACK_SECTION_LOADING_KEYS } from '@/constants/loading-keys';
import { LOADING_SCOPES } from '@/constants/loading-scopes';
import { useScopedLoading, useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { Send } from 'lucide-react';
import { FormEvent, useState } from 'react';
import { itemVariants } from './animation-variants';

export function FeedbackSection() {
	const [feedbackMessage, setFeedbackMessage] = useState('');
	const { setLoading: setSubmittingLoading, getLoading: getSubmittingLoading } = useScopedLoading(
		LOADING_SCOPES.FEEDBACK_SECTION
	);
	const { t } = useTranslation();
	const { toast } = useToast();

	const handleFeedbackSubmit = async (e: FormEvent) => {
		e.preventDefault();

		// Validate required fields
		if (!feedbackMessage) {
			toast({
				title: t('toast.missing_info'),
				description: t('toast.missing_info_desc'),
			});
			return;
		}

		setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, true);

		try {
			const response = await fetch('/api/feedback', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ message: feedbackMessage }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to submit feedback');
			}

			toast({
				title: t('toast.feedback_sent'),
				description: t('toast.feedback_sent_desc'),
			});
			setFeedbackMessage('');
		} catch (error) {
			toast({
				variant: 'destructive',
				title: t('toast.submission_failed'),
				description:
					error instanceof Error ? error.message : t('toast.submission_failed_desc'),
			});
		} finally {
			setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, false);
		}
	};

	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="feedback.title" />
			</h2>
			<motion.form
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.3 }}
				className="space-y-4 text-left"
				onSubmit={handleFeedbackSubmit}
			>
				<motion.div variants={itemVariants}>
					<label htmlFor="feedback" className="block text-sm font-medium mb-1">
						<Translate text="feedback.message" />
					</label>
					<Textarea
						id="feedback"
						value={feedbackMessage}
						onChange={(e) => setFeedbackMessage(e.target.value)}
						className="w-full bg-background/50 dark:bg-card/50 backdrop-blur-sm min-h-[100px] focus:ring-2 focus:ring-primary/20 transition-all duration-200"
						placeholder={t('feedback.placeholder.message')}
						required
					/>
				</motion.div>
				<motion.div variants={itemVariants} className="flex justify-end">
					<Button
						type="submit"
						disabled={getSubmittingLoading('submitFeedback')}
						className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300"
					>
						{getSubmittingLoading('submitFeedback') ? (
							<LoadingSpinner size="sm" />
						) : (
							<Send className="size-4 text-primary-foreground" />
						)}
						<Translate text="feedback.submit" />
					</Button>
				</motion.div>
			</motion.form>
		</div>
	);
}

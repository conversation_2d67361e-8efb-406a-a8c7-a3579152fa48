# Test Directory

This directory contains test files, testing utilities, mocks, and fixtures for comprehensive testing of the application using Vitest and React Testing Library.

## Structure

```
test/
├── setup.ts                                    # Test environment setup
├── vitest-setup.ts                            # Vitest-specific configuration
├── example.test.ts                            # Example test file
├── vitest-example.test.ts                     # Vitest example test
├── collection-overview-disabled-links.test.tsx # Component integration test
├── collection-overview-logic.test.ts          # Business logic test
├── __mocks__/                                 # Mock implementations
├── fixtures/                                  # Test data and fixtures
├── helpers/                                   # Testing utility functions
└── mocks/                                     # Additional mock files
```

## Test Categories

### Test Setup and Configuration
- **setup.ts**: Global test environment configuration and setup
- **vitest-setup.ts**: Vitest-specific configuration and test utilities
- Test environment initialization and cleanup
- Global mocks and test utilities registration

### Component Tests
- **collection-overview-disabled-links.test.tsx**: Component integration testing
- React component rendering and interaction tests
- User interface behavior and accessibility testing
- Component state management and prop handling

### Business Logic Tests
- **collection-overview-logic.test.ts**: Pure business logic testing
- Algorithm and calculation testing
- Data transformation and validation testing
- Service and utility function testing

### Example and Template Tests
- **example.test.ts**: General testing examples and patterns
- **vitest-example.test.ts**: Vitest-specific testing examples
- Testing best practices and patterns demonstration
- Template tests for different testing scenarios

## Testing Infrastructure

### Mocks and Fixtures
- **__mocks__/**: Mock implementations for external dependencies
- **fixtures/**: Test data, sample objects, and test scenarios
- **mocks/**: Additional mock files for specific testing needs
- **helpers/**: Testing utility functions and custom matchers

### Testing Utilities
- Custom testing utilities and helper functions
- Mock factories for creating test data
- Testing setup and teardown utilities
- Assertion helpers and custom matchers

## Key Features

### Testing Framework
- **Vitest**: Fast and modern testing framework
- **React Testing Library**: Component testing with user-centric approach
- **Jest Compatibility**: Jest-compatible APIs and matchers
- **TypeScript Support**: Full TypeScript integration and type checking

### Test Types
- **Unit Tests**: Individual function and component testing
- **Integration Tests**: Component interaction and workflow testing
- **End-to-End Tests**: Full application workflow testing
- **Performance Tests**: Performance and optimization testing

### Testing Patterns
- **Arrange-Act-Assert**: Clear test structure and organization
- **User-Centric Testing**: Testing from user perspective
- **Mock Strategy**: Strategic mocking of external dependencies
- **Test Data Management**: Organized test data and fixtures

## Testing Guidelines

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names that explain the scenario
- Follow Arrange-Act-Assert pattern for test structure
- Keep tests focused and independent
- Use setup and teardown for common test preparation

### Component Testing
- Test component behavior, not implementation details
- Use user-centric queries (getByRole, getByLabelText)
- Test accessibility features and keyboard navigation
- Mock external dependencies and API calls
- Test error states and edge cases

### Business Logic Testing
- Test pure functions with various input scenarios
- Use property-based testing for complex algorithms
- Test error handling and validation logic
- Mock external services and dependencies
- Test performance-critical code paths

### Mock Strategy
- Mock external APIs and services
- Use real implementations for internal utilities
- Create reusable mock factories
- Mock at the appropriate abstraction level
- Keep mocks simple and focused

## Usage Patterns

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { MyComponent } from '@/components';

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

### Business Logic Testing
```typescript
import { calculateProgress } from '@/lib/utils';

describe('calculateProgress', () => {
  it('should calculate progress correctly', () => {
    const result = calculateProgress(5, 10);
    expect(result).toBe(50);
  });
});
```

### Mock Usage
```typescript
import { vi } from 'vitest';
import { apiService } from '@/services';

vi.mock('@/services', () => ({
  apiService: {
    fetchData: vi.fn(),
  },
}));
```

## Development Guidelines

### Test Writing
- Write tests before or alongside implementation (TDD/BDD)
- Keep tests simple, focused, and readable
- Use descriptive test names and clear assertions
- Test both happy path and error scenarios
- Maintain good test coverage without obsessing over 100%

### Test Maintenance
- Regularly review and update tests
- Remove obsolete tests and update outdated ones
- Refactor tests to improve readability and maintainability
- Keep test data and fixtures up to date
- Monitor test performance and optimize slow tests

### Continuous Integration
- Run tests automatically on code changes
- Include tests in code review process
- Monitor test coverage and quality metrics
- Use tests as documentation for expected behavior
- Fail builds on test failures or coverage drops

import { test, expect } from '@playwright/test';

test.describe('Basic E2E Tests', () => {
	test('should load homepage', async ({ page }) => {
		await page.goto('/');

		// Check if the page loads
		await expect(page).toHaveTitle(/Vocab/);
	});

	test('should have health endpoint', async ({ page }) => {
		const response = await page.goto('/api/health');
		expect(response?.status()).toBe(200);

		const healthData = await response?.json();
		expect(healthData).toHaveProperty('status', 'healthy');
	});
});
